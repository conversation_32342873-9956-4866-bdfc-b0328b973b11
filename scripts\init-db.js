import 'dotenv/config'
import { pool, ensureSchema } from '../src/config/database.js'

async function initializeDatabase() {
  try {
    console.log('🔄 Initializing database schema...')
    
    // Create tables and triggers
    await ensureSchema()
    
    console.log('✅ Database schema initialized successfully!')
    console.log('📊 Tables created:')
    console.log('   - tasks (with auto-updating timestamps)')
    
    // Test the connection
    const result = await pool.query('SELECT NOW() as current_time')
    console.log('🕒 Database connection test:', result.rows[0].current_time)
    
    // Check if tables exist
    const tables = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `)
    
    console.log('📋 Available tables:')
    tables.rows.forEach(row => {
      console.log(`   - ${row.table_name}`)
    })
    
  } catch (error) {
    console.error('❌ Error initializing database:', error.message)
    process.exit(1)
  } finally {
    await pool.end()
    console.log('🔌 Database connection closed')
  }
}

// Run the initialization
initializeDatabase()
