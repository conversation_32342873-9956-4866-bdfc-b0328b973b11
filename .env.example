# Application Configuration
APP_PORT=3000
APP_DEBUG=true
API_VERSION=v1
NODE_ENV=development
APP_NAME=tasks-api

# Database Configuration
# Option 1: Individual connection parameters
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your_password_here
DB_NAME=tasks_db

# Option 2: Connection string (takes precedence if provided)
# DATABASE_URL=postgres://username:password@localhost:5432/tasks_db

# Database Pool Configuration
DB_POOL_MAX=10
DB_IDLE_MS=30000
DB_CONNECT_MS=5000

# Optional: Admin database URL for automatic database creation
# DB_ADMIN_URL=postgres://postgres:password@localhost:5432/postgres

# CORS Configuration (optional)
# WHITELIST_URLS=http://localhost:3000,http://localhost:3001
