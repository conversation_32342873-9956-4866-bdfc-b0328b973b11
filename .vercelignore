# Dependencies
node_modules

# Development files
.env.local
.env.development
.env.test

# Build outputs
dist
build

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage

# IDE files
.vscode
.idea
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Local development
.env.development.local
.env.local
.env.test.local

# Temporary files
tmp
temp
